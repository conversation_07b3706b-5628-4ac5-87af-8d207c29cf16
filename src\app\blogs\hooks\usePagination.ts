'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

interface UsePaginationProps<T> {
  data: T[];
  itemsPerPage?: number;
  enableUrlSync?: boolean;
}

interface UsePaginationReturn<T> {
  currentPage: number;
  totalPages: number;
  currentItems: T[];
  startIndex: number;
  endIndex: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  goToPage: (page: number) => void;
  goToNextPage: () => void;
  goToPreviousPage: () => void;
  goToFirstPage: () => void;
  goToLastPage: () => void;
}

export function usePagination<T>({
  data,
  itemsPerPage = 10,
  enableUrlSync = false,
}: UsePaginationProps<T>): UsePaginationReturn<T> {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get initial page from URL if URL sync is enabled
  const getInitialPage = () => {
    if (enableUrlSync && searchParams) {
      const pageFromUrl = searchParams.get('page');
      const parsedPage = pageFromUrl ? parseInt(pageFromUrl, 10) : 1;
      return parsedPage > 0 ? parsedPage : 1;
    }
    return 1;
  };

  const [currentPage, setCurrentPage] = useState(getInitialPage);

  // Memoize calculations for performance
  const paginationData = useMemo(() => {
    const totalPages = Math.ceil(data.length / itemsPerPage);

    // Ensure current page is within valid range
    const validCurrentPage = Math.min(
      Math.max(1, currentPage),
      totalPages || 1
    );

    const startIndex = (validCurrentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, data.length);
    const currentItems = data.slice(startIndex, endIndex);

    const hasNextPage = validCurrentPage < totalPages;
    const hasPreviousPage = validCurrentPage > 1;

    return {
      totalPages,
      validCurrentPage,
      startIndex,
      endIndex,
      currentItems,
      hasNextPage,
      hasPreviousPage,
    };
  }, [data, itemsPerPage, currentPage]);

  // Update URL when page changes (if URL sync is enabled)
  useEffect(() => {
    if (enableUrlSync && paginationData.validCurrentPage !== currentPage) {
      setCurrentPage(paginationData.validCurrentPage);
    }
  }, [paginationData.validCurrentPage, currentPage, enableUrlSync]);

  // Sync URL with current page
  const updateUrl = (page: number) => {
    if (enableUrlSync) {
      if (searchParams) {
        const params = new URLSearchParams(searchParams.toString());
        if (page === 1) {
          params.delete('page');
        } else {
          params.set('page', page.toString());
        }

        const newUrl = params.toString() ? `?${params.toString()}` : '';
        router.push(newUrl, { scroll: false });
      } else {
        // Fallback when searchParams is null
        const newUrl = page === 1 ? '' : `?page=${page}`;
        router.push(newUrl, { scroll: false });
      }
    }
  };

  const goToPage = (page: number) => {
    const validPage = Math.min(
      Math.max(1, page),
      paginationData.totalPages || 1
    );
    setCurrentPage(validPage);
    updateUrl(validPage);
  };

  const goToNextPage = () => {
    if (paginationData.hasNextPage) {
      goToPage(paginationData.validCurrentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (paginationData.hasPreviousPage) {
      goToPage(paginationData.validCurrentPage - 1);
    }
  };

  const goToFirstPage = () => {
    goToPage(1);
  };

  const goToLastPage = () => {
    goToPage(paginationData.totalPages);
  };

  return {
    currentPage: paginationData.validCurrentPage,
    totalPages: paginationData.totalPages,
    currentItems: paginationData.currentItems,
    startIndex: paginationData.startIndex,
    endIndex: paginationData.endIndex,
    hasNextPage: paginationData.hasNextPage,
    hasPreviousPage: paginationData.hasPreviousPage,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    goToFirstPage,
    goToLastPage,
  };
}
