'use client';
import Image from 'next/image';
import Link from 'next/link';
import { Menu, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const pathname = usePathname();
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Check if we're on a blog page or terms of service page
  const isBlogPage = pathname?.includes('/blogs/') || pathname?.includes('/terms-of-service') || pathname?.includes('/privacy-policy') || false;

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 ${
        isBlogPage
          ? 'bg-[#063065]'
          : isScrolled
            ? 'bg-[#063065]'
            : 'bg-transparent'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-0">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0 relative w-[97px] h-[21px] md:w-[146.34px] md:h-[32px] ">
            <Link href="/">
              <Image
                src="/eh-logo.svg"
                alt="Expert Hire logo"
                width={146.34}
                height={27.17}
                priority
              />
            </Link>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              href="/blogs"
              className="text-gray-200 hover:text-white transition-colors"
            >
              Blogs
            </Link>
            <Link
              href="/terms-of-service"
              className="text-gray-200 hover:text-white transition-colors"
            >
              Terms of Service
            </Link>
            <Link
              href="/privacy-policy"
              className="text-gray-200 hover:text-white transition-colors"
            >
              Privacy Policy
            </Link>
            {/* <Link
              href="/careers"
              className="text-gray-200 hover:text-white transition-colors"
            >
              Careers
            </Link> */}
          </div>

          {/* CTA Buttons */}
          <div className="flex items-center space-x-4">
            <Link
              href="https://app.experthire.fr/"
              className="text-white border-2 border-white text-sm md:text-base px-2 py-1 md:px-4 md:py-2 rounded-lg hover:bg-white hover:text-[#063065] transition-colors"
            >
              Log in
            </Link>
            <Link
              href="https://app.experthire.fr/"
              className="bg-white text-[#063065] text-sm md:text-base px-2 py-1 md:px-4 md:py-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              Join Us
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <div className="relative md:hidden">
            <button className="md:hidden" onClick={() => setIsOpen(!isOpen)}>
              {isOpen ? (
                <X className="w-6 h-6 text-white" />
              ) : (
                <Menu className="w-6 h-6 text-white" />
              )}
            </button>
          </div>
        </div>
      </div>
      {isOpen && (
        <div className="md:hidden absolute right-0 space-y-1 text-center text-white w-40 bg-gradient-to-b from-[#063065] to-[#094288] rounded-lg border-4 border-white shadow-lg py-2 z-50">
          <Link
            href="/blogs"
            className="block px-4 py-2 text-gray-200 transition-colors"
          >
            Blogs
          </Link>
          <Link
            href="/terms-of-service"
            className="block px-4 py-2 text-gray-200 transition-colors"
          >
            Terms of Service
          </Link>
          <Link
            href="/privacy-policy"
            className="block px-4 py-2 text-gray-200 transition-colors"
          >
            Privacy Policy
          </Link>
          {/* <Link
            href="/careers"
            className="block px-4 py-2 text-gray-200 transition-colors"
          >
            Careers
          </Link> */}
        </div>
      )}
    </nav>
  );
};

export default Navbar;
