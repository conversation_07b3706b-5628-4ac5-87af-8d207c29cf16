@import 'tailwindcss';

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #063065;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    /* --background: #0a0a0a; */
    /* --foreground: #ededed; */
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Primary Button Styles */
.btn-primary {
  @apply bg-[#063065] text-white px-4 py-2 rounded-lg hover:bg-[#084283] transition-colors;
}

.btn-primary-outline {
  @apply border-2 border-[#063065] text-[#063065] px-4 py-2 rounded-lg hover:bg-[#063065] hover:text-white transition-colors;
}
