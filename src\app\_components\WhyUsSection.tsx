import Image from 'next/image';
import { whyUsCardsData } from '@/lib/config/data';

const WhyUsSection = () => {
  return (
    <section className="bg-[#063065] lg:bg-[linear-gradient(to_bottom,#063065_75%,white_25%)]">
      <div className="container mx-auto max-w-5xl py-12 md:py-16 lg:py-24 ">
        <h1 className="text-2xl md:text-3xl lg:text-[40px] font-bold text-center text-white">
          Why Experthire
        </h1>
        <p className="text-base font-medium text-center px-4 md:px-8 py-6 lg:py-8 text-white max-w-3xl mx-auto">
          Empower your expertise with our distinct platform, connecting you to
          quality projects and fostering meaningful collaborations with
          industry-leading clients. Furthermore, you as a recruiter have
          opportunities to have any kind of expert based on level of skills,
          languages, location whether local or remote.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 px-4 md:px-8 place-items-center">
          {whyUsCardsData.map((card, index) => (
            <div
              key={index}
              className="bg-[#FFF] h-[268px] w-[300px] rounded-[16px] flex flex-col gap-[28px] px-[32px] py-[38px] items-center justify-center shadow-md transition-transform duration-300 hover:scale-105"
            >
              <div className="bg-[#FFF] h-[120px] w-[120px] rounded-[16px] flex items-center justify-center shadow-[0px_8px_32px_-3px_rgba(46,105,178,0.10)]">
                <Image src={card.icon} alt="icon" width={32} height={32} />
              </div>
              <p className="text-[16px] font-[500]  text-center">
                {card.title}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
export default WhyUsSection;
