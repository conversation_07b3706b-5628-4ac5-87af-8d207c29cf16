'use client';

import { Post } from '@/types/schema';
import Link from 'next/link';

// Simple utility functions for tag processing
const getPopularTags = (
  posts: Post[],
  limit: number = 20
): Array<{ tag: string; count: number }> => {
  const tagCount: Record<string, number> = {};

  posts.forEach((post: Post) => {
    if (post.tags && Array.isArray(post.tags)) {
      post.tags.forEach((tag: string) => {
        tagCount[tag] = (tagCount[tag] || 0) + 1;
      });
    }
  });

  return Object.entries(tagCount)
    .map(([tag, count]) => ({ tag, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, limit);
};

const tagToSlug = (tag: string): string => {
  return tag
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]/g, '');
};

interface TagsSectionProps {
  posts: Post[];
  currentTag?: string;
  showTitle?: boolean;
  maxTags?: number;
}

const TagsSection = ({
  posts,
  currentTag,
  showTitle = true,
  maxTags = 15,
}: TagsSectionProps) => {
  // Get popular tags from posts
  const popularTags = getPopularTags(posts, maxTags);

  if (popularTags.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        {showTitle && (
          <h3 className="text-lg font-bold text-gray-900 mb-4">Tags</h3>
        )}
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">
            <svg
              className="w-12 h-12 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
              />
            </svg>
          </div>
          <p className="text-gray-500 text-sm">No tags available yet.</p>
          <p className="text-gray-400 text-xs mt-1">
            Add tags to your blog posts in Sanity Studio.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      {showTitle && (
        <h3 className="text-lg font-bold text-gray-900 mb-4">Popular Tags</h3>
      )}

      <div className="flex flex-wrap gap-2">
        {popularTags.map(({ tag, count }) => {
          const isActive = currentTag === tag;
          const tagSlug = tagToSlug(tag);

          return (
            <Link
              key={tag}
              href={`/blogs/tag/${tagSlug}`}
              className={`
                inline-flex items-center px-3 py-1.5 text-sm rounded-full transition-all duration-200 cursor-pointer
                ${
                  isActive
                    ? 'bg-[#063065] text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-[#063065] hover:text-white hover:shadow-md'
                }
              `}
              title={`${count} post${count !== 1 ? 's' : ''} tagged with "${tag}"`}
            >
              <span className="capitalize font-medium">{tag}</span>
              {count > 1 && (
                <span
                  className={`
                  ml-2 text-xs px-1.5 py-0.5 rounded-full font-medium
                  ${
                    isActive
                      ? 'bg-white/20 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }
                `}
                >
                  {count}
                </span>
              )}
            </Link>
          );
        })}
      </div>

      {popularTags.length === maxTags && (
        <div className="mt-4 text-xs text-gray-500 text-center">
          Showing top {maxTags} tags
        </div>
      )}
    </div>
  );
};

export default TagsSection;
