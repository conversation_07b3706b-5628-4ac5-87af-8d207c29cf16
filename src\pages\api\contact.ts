import nodemailer from 'nodemailer';
import { NextApiRequest, NextApiResponse } from 'next';
import { contactSchema } from '@/lib/schemas/contactSchema';
import { smtpConfig } from '@/lib/config';
import { InMemoryRateLimiter } from '@/services/rateLimiter';

const rateLimiter = new InMemoryRateLimiter({
  maxRequests: 5,
  windowMs: 60 * 60 * 1000,
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }
  let ip = (req.headers['x-forwarded-for'] || req.socket.remoteAddress) as
    | string
    | undefined;

  if (!ip) {
    console.warn('Could not determine client IP address.');
    return res.status(400).json({ message: 'Unable to determine IP address' });
  }

  if (ip.includes(',')) {
    ip = ip.split(',')[0].trim();
  }

  const limitStatus = rateLimiter.checkLimit(ip as string);
  if (!limitStatus.allowed) {
    return res.status(429).json({
      message: 'Too many requests. Please try again later.',
      resetTime: limitStatus.resetTime,
    });
  }
  const validatedData = contactSchema.safeParse(req.body);
  if (!validatedData.success) {
    return res
      .status(400)
      .json({ message: 'Invalid data', errors: validatedData.error });
  }
  const { name, email, phone, message } = validatedData.data;
  try {
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: smtpConfig.user,
        pass: smtpConfig.pass,
      },
    });
    await transporter.sendMail({
      from: smtpConfig.user,
      to: '<EMAIL>',
      subject: `New Contact Form Submission from ${name}`,
      html: `
        <p><strong>Name:</strong> ${name}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Phone:</strong> ${phone || 'N/A'}</p>
        <p><strong>Message:</strong><br/>${message}</p>
      `,
    });
    return res.status(200).json({ message: 'Message sent successfully!' });
  } catch (error) {
    console.error('Error sending email: ', error);
    return res.status(500).json({ message: 'Failed to send message' });
  }
}
