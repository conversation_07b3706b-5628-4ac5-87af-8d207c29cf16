type RateLimiterOptions = {
  maxRequests: number;
  windowMs: number;
};

type RateLimitStatus = {
  allowed: boolean;
  remaining: number;
  resetTime: number;
};

export class InMemoryRateLimiter {
  private requests: Map<string, { count: number; timer: NodeJS.Timeout }> =
    new Map();
  private maxRequests: number;
  private windowMs: number;

  constructor(options: RateLimiterOptions) {
    this.maxRequests = options.maxRequests;
    this.windowMs = options.windowMs;
  }

  checkLimit(ip: string): RateLimitStatus {
    const now = Date.now();
    const entry = this.requests.get(ip);

    if (!entry) {
      const timer = setTimeout(() => {
        this.requests.delete(ip);
      }, this.windowMs);

      this.requests.set(ip, { count: 1, timer });
      return {
        allowed: true,
        remaining: this.maxRequests - 1,
        resetTime: now + this.windowMs,
      };
    }
    if (entry.count >= this.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: now + this.windowMs,
      };
    }
    entry.count += 1;
    return {
      allowed: true,
      remaining: this.maxRequests - entry.count,
      resetTime: now + this.windowMs,
    };
  }
}
