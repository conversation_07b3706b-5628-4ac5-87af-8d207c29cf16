import Link from 'next/link';
import { Arrow<PERSON>eft, Tag } from 'lucide-react';
import { Navbar, Footer } from '../../../_components';

export default function TagNotFound() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center px-4">
          <div className="mb-8">
            <Tag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Tag Not Found
            </h1>
            <p className="text-gray-600">
              No posts found with this tag. The tag might not exist or all posts with this tag have been removed.
            </p>
          </div>
          
          <div className="space-y-4">
            <Link
              href="/blogs"
              className="inline-flex items-center px-6 py-3 bg-[#063065] text-white rounded-lg hover:bg-[#094288] transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to All Posts
            </Link>
            
            <div className="text-sm text-gray-500">
              <p>You can browse all available tags on the main blog page.</p>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
