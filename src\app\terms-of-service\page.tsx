import { Navbar, Footer } from '../_components';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Terms of Service - Expert Hire',
  description: 'Terms of Service for Expert Hire platform',
};

export default function TermsOfService() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-white">
        <div className="max-w-4xl mx-auto px-6 pt-24 pb-16">
          {/* Header with back button and title */}
          <div className="flex items-center mb-2">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 hover:text-gray-800 transition-colors mr-4"
            >
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <h1 className="text-3xl font-semibold text-gray-900">
              Terms of Service
            </h1>
          </div>

          {/* Introduction */}
          <p className="text-gray-700 text-base leading-relaxed mb-8">
            Welcome to ExpertHire, a freelancing platform that connects hirers
            and experts in various fields, including project management, cyber
            security, digital marketing, software development, database
            analysis, training, and physical security.
          </p>

          {/* Content sections with left blue sidebar */}
          <div className="relative">
            {/* Blue sidebar on the left */}
            <div className="absolute left-0 top-0 w-1 h-full bg-[#063065]"></div>

            {/* Content with left padding */}
            <div className="pl-8 space-y-8">
              {/* Description of Service */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Description of Service
                </h2>
                <p className="text-gray-700 text-base leading-relaxed">
                  ExpertHire is a platform that allows hirers to post projects
                  or jobs and experts to complete those projects or jobs.
                  ExpertHire is not a party to any agreements made between
                  hirers and experts. We do not endorse any hirer or expert and
                  do not guarantee the quality, accuracy, or legality of any
                  project, job, or service advertised, the truth or accuracy of
                  any hirer&apos;s or expert&apos;s profiles, or the ability of
                  experts to complete projects or jobs. We do not provide any
                  guarantee regarding the safety or security of transactions,
                  the accuracy of project or job descriptions, or the
                  suitability of any expert for a particular project or job.
                  Hirers and experts are solely responsible for any and all
                  communications, agreements, and transactions between them.
                </p>
              </div>

              {/* Registration and Account Security */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Registration and Account Security
                </h2>
                <p className="text-gray-700 text-base leading-relaxed">
                  To use certain features of ExpertHire, you must register for
                  an account. You agree to provide accurate, current, and
                  complete information during the registration process and if
                  necessary, to update such information to keep it accurate,
                  current, and complete. You are responsible for maintaining the
                  confidentiality of your account credentials and for all
                  activities that occur under your account. You agree to
                  immediately notify ExpertHire of any unauthorized use, or
                  suspected unauthorized use, of your account or any other
                  breach of security.
                </p>
              </div>

              {/* Posting Jobs */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Posting Jobs
                </h2>
                <p className="text-gray-700 text-base leading-relaxed">
                  ExpertHire allows hirers to post jobs on the platform, subject
                  to the guidelines outlined in this section.
                </p>
              </div>

              {/* Types of Jobs Allowed */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Types of Jobs Allowed
                </h2>
                <p className="text-gray-700 text-base leading-relaxed">
                  Only legitimate and lawful jobs are allowed to be posted on
                  ExpertHire. Illegal or unethical jobs will be removed
                  immediately upon discovery, and the hirer will be subject to
                  account suspension or termination. Examples of jobs that are
                  not allowed on ExpertHire include, but are not limited to,
                  those involving:
                </p>
              </div>

              {/* Contact Information */}
              <div className="pt-4">
                <p className="text-gray-700 text-base leading-relaxed">
                  If you have any question or concern about our terms of service
                  please contact us at{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-[#063065] hover:underline"
                  >
                    [<EMAIL>]
                  </a>
                  . We will respond to your inquiry as soon as possible. By
                  using the ExpertHire website, mobile app, or any of our
                  services, you agree to be bound by these Terms of Service
                  (&quot;Terms&quot;). Please read these Terms carefully before
                  using ExpertHire. If you do not agree to these Terms, do not
                  use ExpertHire.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
