import { Navbar, Footer } from '../_components';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Terms of Service - Expert Hire',
  description: 'Terms of Service for Expert Hire platform',
};

export default function TermsOfService() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-white">
        {/* Header Section */}
        <div className="bg-[#063065] pt-20 pb-16">
          <div className="max-w-4xl mx-auto px-6">
            <Link
              href="/"
              className="inline-flex items-center text-white hover:text-gray-200 transition-colors mb-8"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Link>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Terms of Service
            </h1>
            <p className="text-gray-200 text-lg">Last updated on 1/12/2021</p>
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-4xl mx-auto px-6 py-16">
          <div className="prose prose-lg max-w-none">
            <p className="text-gray-700 text-lg leading-relaxed mb-8">
              Welcome to ExpertHire, a freelancing platform that connects hirers
              and experts in various fields, including project management, cyber
              security, digital marketing, software development, database
              analysis, training, and physical security.
            </p>

            <div className="space-y-12">
              {/* Description of Service */}
              <section>
                <h2 className="text-2xl font-bold text-[#063065] mb-6 border-l-4 border-[#063065] pl-4">
                  Description of Service
                </h2>
                <p className="text-gray-700 leading-relaxed mb-4">
                  ExpertHire is a platform that allows hirers to post projects
                  or jobs and experts to complete those projects or jobs.
                  ExpertHire is not a party to any agreements made between
                  hirers and experts. We do not endorse any hirer or expert and
                  do not guarantee the quality, accuracy, or legality of any
                  project, job, or service advertised, the truth or accuracy of
                  any hirer&apos;s or expert&apos;s profiles, or the ability of
                  experts to complete projects or jobs. We do not provide any
                  guarantee regarding the safety or security of transactions,
                  the accuracy of project or job descriptions, or the
                  suitability of any expert for a particular project or job.
                  Hirers and experts are solely responsible for any and all
                  communications, agreements, and transactions between them.
                </p>
              </section>

              {/* Registration and Account Security */}
              <section>
                <h2 className="text-2xl font-bold text-[#063065] mb-6 border-l-4 border-[#063065] pl-4">
                  Registration and Account Security
                </h2>
                <p className="text-gray-700 leading-relaxed mb-4">
                  To use certain features of ExpertHire, you must register for
                  an account. You agree to provide accurate, current, and
                  complete information during the registration process and if
                  necessary, to update such information to keep it accurate,
                  current, and complete. You are responsible for maintaining the
                  confidentiality of your account credentials and for all
                  activities that occur under your account. You agree to
                  immediately notify ExpertHire of any unauthorized use, or
                  suspected unauthorized use, of your account or any other
                  breach of security.
                </p>
              </section>

              {/* Posting Jobs */}
              <section>
                <h2 className="text-2xl font-bold text-[#063065] mb-6 border-l-4 border-[#063065] pl-4">
                  Posting Jobs
                </h2>
                <p className="text-gray-700 leading-relaxed mb-4">
                  ExpertHire allows hirers to post jobs on the platform, subject
                  to the guidelines outlined in this section.
                </p>
              </section>

              {/* Types of Jobs Allowed */}
              <section>
                <h2 className="text-2xl font-bold text-[#063065] mb-6 border-l-4 border-[#063065] pl-4">
                  Types of Jobs Allowed
                </h2>
                <p className="text-gray-700 leading-relaxed mb-4">
                  Only legitimate and lawful jobs are allowed to be posted on
                  ExpertHire. Illegal or unethical jobs will be removed
                  immediately upon discovery, and the hirer will be subject to
                  account suspension or termination. Examples of jobs that are
                  not allowed on ExpertHire include, but are not limited to,
                  those involving:
                </p>
              </section>

              {/* Contact Information */}
              <section>
                <p className="text-gray-700 leading-relaxed">
                  If you have any question or concern about our terms of service
                  please contact us at{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-[#063065] hover:underline font-medium"
                  >
                    [<EMAIL>]
                  </a>
                  . We will respond to your inquiry as soon as possible. By
                  using the ExpertHire website, mobile app, or any of our
                  services, you agree to be bound by these Terms of Service
                  (&quot;Terms&quot;). Please read these Terms carefully before
                  using ExpertHire. If you do not agree to these Terms, do not
                  use ExpertHire.
                </p>
              </section>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
