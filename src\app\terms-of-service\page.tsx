import { Navbar, Footer } from '../_components';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Terms of Service - Expert Hire',
  description: 'Terms of Service for Expert Hire platform',
};

export default function TermsOfService() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-[linear-gradient(to_bottom,#063065_50%,#f3f4f6_50%)]">
        <div className="max-w-7xl mx-auto px-6 pt-24 pb-16">
          {/* Header with back button and title */}
          <div className="flex items-center mb-2">
            <Link
              href="/"
              className="inline-flex items-center text-white hover:text-gray-200 transition-colors mr-4"
            >
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <h1 className="text-3xl font-semibold text-white">
              Terms of Service
            </h1>
          </div>

          {/* Last updated */}
          <p className="text-gray-200 text-sm mb-8">
            Last updated on 1/12/2021
          </p>

          {/* Introduction */}
          <p className="text-white text-base leading-relaxed mb-12">
            Welcome to ExpertHire, a freelancing platform that connects hirers
            and experts in various fields, including project management, cyber
            security, digital marketing, software development, database
            analysis, training, and physical security.
          </p>

          {/* Side-by-side layout similar to FAQ */}
          <div className="flex flex-col lg:flex-row gap-8 lg:relative lg:z-10">
            {/* Left side - Sections List */}
            <div className="w-full lg:h-[500px] lg:w-[45%] lg:absolute lg:top-0 lg:left-0 lg:z-10 bg-white shadow-lg rounded-2xl relative">
              <div className="h-full p-6">
                <h3 className="text-xl font-semibold text-[#063065] mb-6">
                  Terms Sections
                </h3>
                <div className="space-y-4">
                  <div className="p-4 bg-[#063065]/10 rounded-lg border-l-4 border-[#063065]">
                    <h4 className="font-semibold text-[#063065] mb-2">
                      Description of Service
                    </h4>
                    <p className="text-sm text-gray-600">
                      Platform overview and service limitations
                    </p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg border-l-4 border-gray-300">
                    <h4 className="font-semibold text-gray-700 mb-2">
                      Registration and Account Security
                    </h4>
                    <p className="text-sm text-gray-600">
                      Account creation and security requirements
                    </p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg border-l-4 border-gray-300">
                    <h4 className="font-semibold text-gray-700 mb-2">
                      Posting Jobs
                    </h4>
                    <p className="text-sm text-gray-600">
                      Guidelines for job posting on the platform
                    </p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg border-l-4 border-gray-300">
                    <h4 className="font-semibold text-gray-700 mb-2">
                      Types of Jobs Allowed
                    </h4>
                    <p className="text-sm text-gray-600">
                      Permitted and prohibited job categories
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Content Card */}
            <div className="w-full h-[500px] p-6 lg:w-[60%] lg:absolute lg:top-0 lg:right-0 lg:pl-12 lg:pr-8 lg:pt-8 bg-white shadow-lg rounded-2xl relative">
              <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-[#063065]/20 scrollbar-track-transparent pr-4">
                <div className="space-y-8">
                  {/* Description of Service */}
                  <div>
                    <h2 className="text-lg font-semibold text-[#063065] mb-4">
                      Description of Service
                    </h2>
                    <p className="text-gray-700 text-base leading-relaxed mb-6">
                      ExpertHire is a platform that allows hirers to post
                      projects or jobs and experts to complete those projects or
                      jobs. ExpertHire is not a party to any agreements made
                      between hirers and experts. We do not endorse any hirer or
                      expert and do not guarantee the quality, accuracy, or
                      legality of any project, job, or service advertised, the
                      truth or accuracy of any hirer&apos;s or expert&apos;s
                      profiles, or the ability of experts to complete projects
                      or jobs. We do not provide any guarantee regarding the
                      safety or security of transactions, the accuracy of
                      project or job descriptions, or the suitability of any
                      expert for a particular project or job. Hirers and experts
                      are solely responsible for any and all communications,
                      agreements, and transactions between them.
                    </p>
                  </div>

                  {/* Registration and Account Security */}
                  <div>
                    <h2 className="text-lg font-semibold text-[#063065] mb-4">
                      Registration and Account Security
                    </h2>
                    <p className="text-gray-700 text-base leading-relaxed mb-6">
                      To use certain features of ExpertHire, you must register
                      for an account. You agree to provide accurate, current,
                      and complete information during the registration process
                      and if necessary, to update such information to keep it
                      accurate, current, and complete. You are responsible for
                      maintaining the confidentiality of your account
                      credentials and for all activities that occur under your
                      account. You agree to immediately notify ExpertHire of any
                      unauthorized use, or suspected unauthorized use, of your
                      account or any other breach of security.
                    </p>
                  </div>

                  {/* Posting Jobs */}
                  <div>
                    <h2 className="text-lg font-semibold text-[#063065] mb-4">
                      Posting Jobs
                    </h2>
                    <p className="text-gray-700 text-base leading-relaxed mb-6">
                      ExpertHire allows hirers to post jobs on the platform,
                      subject to the guidelines outlined in this section.
                    </p>
                  </div>

                  {/* Types of Jobs Allowed */}
                  <div>
                    <h2 className="text-lg font-semibold text-[#063065] mb-4">
                      Types of Jobs Allowed
                    </h2>
                    <p className="text-gray-700 text-base leading-relaxed mb-6">
                      Only legitimate and lawful jobs are allowed to be posted
                      on ExpertHire. Illegal or unethical jobs will be removed
                      immediately upon discovery, and the hirer will be subject
                      to account suspension or termination. Examples of jobs
                      that are not allowed on ExpertHire include, but are not
                      limited to, those involving:
                    </p>
                  </div>

                  {/* Contact Information */}
                  <div>
                    <h2 className="text-lg font-semibold text-[#063065] mb-4">
                      Contact Information
                    </h2>
                    <p className="text-gray-700 text-base leading-relaxed">
                      If you have any question or concern about our terms of
                      service please contact us at{' '}
                      <a
                        href="mailto:<EMAIL>"
                        className="text-[#063065] hover:underline font-medium"
                      >
                        [<EMAIL>]
                      </a>
                      . We will respond to your inquiry as soon as possible. By
                      using the ExpertHire website, mobile app, or any of our
                      services, you agree to be bound by these Terms of Service
                      (&quot;Terms&quot;). Please read these Terms carefully
                      before using ExpertHire. If you do not agree to these
                      Terms, do not use ExpertHire.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
