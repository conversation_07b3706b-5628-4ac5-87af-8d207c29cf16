import { groq } from 'next-sanity';

export const getAllPostsQuery = groq`  *[_type == "post" && defined(slug.current)] {
    _id,
    title,
    slug,
    mainImage,
    author->,
    categories[]->,
    tags,
    excerpt,
    body,
    _createdAt
  } | order(_createdAt desc)
`;

export const getPostBySlugQuery = groq`  *[_type == "post" && slug.current == $slug][0] {
    _id,
    title,
    slug,
    mainImage,
    author->,
    categories[]->,
    tags,
    excerpt,
    body,
    _createdAt
  }
`;

export const getPostsByCategoryQuery = groq`  *[_type == "post" && defined(slug.current) && $categorySlug in categories[]->slug.current] {
    _id,
    title,
    slug,
    mainImage,
    author->,
    categories[]->,
    tags,
    excerpt,
    body,
    _createdAt
  } | order(_createdAt desc)
`;

export const getPostsByCategoryTitleQuery = groq`  *[_type == "post" && defined(slug.current) && $categoryTitle in categories[]->title] {
    _id,
    title,
    slug,
    mainImage,
    author->,
    categories[]->,
    tags,
    excerpt,
    body,
    _createdAt
  } | order(_createdAt desc)
`;

export const getPostsByTagQuery = groq`  *[_type == "post" && defined(slug.current) && $tag in tags] {
    _id,
    title,
    slug,
    mainImage,
    author->,
    categories[]->,
    tags,
    excerpt,
    body,
    _createdAt
  } | order(_createdAt desc)
`;

export const getAllCategoriesQuery = groq`
  *[_type == "category"] {
    _id,
    title,
    slug,
    description
  } | order(title asc)
`;
