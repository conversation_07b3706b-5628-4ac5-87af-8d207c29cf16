import { sanityFetch } from '@/sanity/lib/live';
import { getPostBySlugQuery } from '@/sanity/lib/queries';
import { urlForImage } from '@/sanity/lib/image';
import { Post } from '@/types/schema';
import Image from 'next/image';
import { PortableText } from '@portabletext/react';
import { portableTextComponents } from '@/components/PortableTextComponents';
import { Navbar, Footer } from '../../_components';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import type { Metadata } from 'next';

type tParams = Promise<{ slug: string }>;

// Generate metadata
export async function generateMetadata({
  params,
}: {
  params: tParams;
}): Promise<Metadata> {
  const { slug } = await params;
  const post = await getBlogPost(slug);
  return {
    title: post?.title || 'Blog Post',
    description: post?.excerpt,
  };
}

async function getBlogPost(slug: string): Promise<Post | null> {
  const result = await sanityFetch({
    query: getPostBySlugQuery,
    params: { slug },
  });
  return result.data;
}

// Page component with Promise-based params
export default async function BlogPost({ params }: { params: tParams }) {
  const { slug } = await params;
  const post = await getBlogPost(slug);

  if (!post) {
    return <div>Post not found</div>;
  }

  return (
    <>
      <Navbar />
      <main className="min-h-screen">
        {/* Back to Blog Link */}
        <section className="bg-gray-50 py-8">
          <div className="max-w-4xl mx-auto px-4 pt-14 sm:px-6 lg:px-8">
            <Link
              href="/blogs"
              className="inline-flex items-center text-[#063065] hover:text-[#094288] transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Blog
            </Link>
          </div>
        </section>

        {/* Article Content */}
        <article className="py-4 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {' '}
            {/* Categories and Creation Time */}
            <div className="mb-4 flex items-center gap-4 text-sm text-gray-500">
              {post.categories?.[0]?.title && (
                <span className="inline-flex items-center">
                  <svg
                    className="w-4 h-4 mr-1"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {post.categories[0].title}
                </span>
              )}{' '}
              {/* Creation Time and Author */}
              {post._createdAt && (
                <span className="inline-flex items-center">
                  <svg
                    className="w-4 h-4 mr-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  {new Date(post._createdAt).toLocaleDateString('en-US', {
                    month: 'long',
                    day: 'numeric',
                    year: 'numeric',
                  })}
                  {post.author?.name && (
                    <span className="ml-1">
                      by{' '}
                      <span className="text-[#063065]">{post.author.name}</span>
                    </span>
                  )}
                </span>
              )}
            </div>
            {/* Title */}
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 leading-tight">
              {post.title}
            </h1>
            {/* Meta Information */}
            <div className="flex items-center text-gray-600 mb-8 pb-8 border-b border-gray-200">
              {' '}
              {post.author && (
                <span className="font-medium">By {post.author.name}</span>
              )}
            </div>
            {/* Featured Image */}
            {post.mainImage && (
              <div className="relative w-full h-[400px] md:h-[500px] mb-12">
                <Image
                  src={urlForImage(post.mainImage).url()}
                  alt={post.title}
                  fill
                  className="object-cover rounded-lg"
                />
              </div>
            )}
            {/* Article Body */}
            <div className="prose prose-lg max-w-none">
              <PortableText
                value={post.body}
                components={portableTextComponents}
              />
            </div>
          </div>
        </article>
      </main>
      <Footer />
    </>
  );
}
