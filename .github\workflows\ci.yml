name: CI/CD Pipeline

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Build application
        run: npm run build
        env:
          # Add your build-time environment variables here
          NODE_ENV: production
          NEXT_PUBLIC_SANITY_PROJECT_ID: ${{ secrets.SANITY_PROJECT_ID }}
          NEXT_PUBLIC_SANITY_DATASET: ${{ secrets.SANITY_DATASET }}

  docker:
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    needs: build-and-test
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            NODE_ENV=production
            NEXT_PUBLIC_SANITY_PROJECT_ID=${{ secrets.SANITY_PROJECT_ID }}
            NEXT_PUBLIC_SANITY_DATASET=${{ secrets.SANITY_DATASET }}

  deploy:
    needs: docker
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
      - uses: actions/checkout@v4

      - name: Deploy to Hostinger
        uses: appleboy/ssh-action@v1.2.0
        with:
          host: ${{ secrets.HOSTINGER_HOST }}
          username: ${{ secrets.HOSTINGER_USERNAME }}
          password: ${{ secrets.HOSTINGER_PASSWORD }}
          port: 22
          script: |
            echo "🚀 Starting deployment process..."

            # Login to GitHub Container Registry
            echo "🔑 Logging into GitHub Container Registry..."
            echo ${{ secrets.GITHUB_TOKEN }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin

            # Get the full image name
            FULL_IMAGE_NAME="${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:main"
            echo "🔄 Attempting to pull image: $FULL_IMAGE_NAME"

            # Pull the latest image with explicit tag
            docker pull $FULL_IMAGE_NAME || {
              echo "❌ Failed to pull image. Listing available images:"
              docker images
              exit 1
            }

            echo "✅ Successfully pulled new image"

            # Create the network if it doesn't exist
            echo "🔗 Creating network..."
            NETWORK_NAME=eh-network
            docker network create $NETWORK_NAME || true

            # Stop and remove the old container if it exists
            echo "🧹 Cleaning up old container..."
            docker stop eh-cms || true
            docker rm eh-cms || true

            echo "🚀 Starting new container..."
            # Run the new container
            docker run -d \
              --name eh-cms \
              --network $NETWORK_NAME \
              --restart always \
              -e NODE_ENV=production \
              -e SMTP_USER='${{ secrets.SMTP_USER }}' \
              -e SMTP_PASS='${{ secrets.SMTP_PASS }}' \
              -e NEXT_PUBLIC_SANITY_PROJECT_ID=${{ secrets.SANITY_PROJECT_ID }} \
              -e NEXT_PUBLIC_SANITY_DATASET=${{ secrets.SANITY_DATASET }} \
              $FULL_IMAGE_NAME

            echo "🔍 Deployment complete. Checking container status..."
            docker ps | grep eh-cms
