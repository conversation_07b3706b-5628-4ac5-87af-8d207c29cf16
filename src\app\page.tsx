'use client';
import useScrollSpy from '@/hooks/useScrollSpy';
import {
  Navbar,
  HeroSection,
  Footer,
  FaqSection,
  FeatureSection,
  JobCategoriesSection,
  WhyUsSection,
  HowWeWorkSection,
  ContactSection,
  PricingSection,
} from './_components';
import { featureData } from '@/lib/config/data';



export default function Home() {
  const { selectedCategory, handleClick } = useScrollSpy();
  return (
    <>
      <Navbar />
      <main className="min-h-screen">
        <HeroSection />
        <WhyUsSection />
        <JobCategoriesSection
          categories={featureData}
          selected={selectedCategory}
          handleClick={handleClick}
        />
        <FeatureSection featureData={featureData} />
        <HowWeWorkSection />
        <PricingSection />
        <FaqSection />
        <ContactSection />
      </main>
      <Footer />
    </>
  );
}
