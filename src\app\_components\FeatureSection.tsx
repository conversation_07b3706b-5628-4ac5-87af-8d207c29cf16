'use client';
import Image from 'next/image';
import Link from 'next/link';
type Feature = {
  id: string;
  title: string;
  description: string;
  image: string;
};

type Props = {
  featureData: Feature[];
};

const FeatureSection = ({ featureData }: Props) => {
  return (
    <div className="container mx-auto py-8 md:py-12">
      {featureData?.map((feature, index) => (
        <div
          key={index}
          id={feature.id}
          className={`flex flex-col-reverse gap-6 md:gap-8 md:justify-between mb-32 md:mb-46 items-center px-4 md:px-8 ${
            index % 2 == 1 ? 'md:flex-row-reverse' : 'md:flex-row'
          }`}
        >
          <div className="flex flex-col gap-4 md:gap-8 w-full items-center md:items-start lg:w-1/2 max-w-[550px] ">
            <h2 className="font-bold text-[#31353B] text-2xl md:text-3xl">
              {feature.title}
            </h2>
            <p className="font-medium text-[#31353B] text-sm sm:text-base md:text-lg">
              {feature.description}
            </p>
            <div className="flex gap-6 md:gap-8 flex-wrap">
              <Link href="https://app.experthire.fr/" className="font-bold text-center hover:text-[#31353B] text-sm sm:text-base px-4 sm:px-6 py-2 sm:py-3 border rounded-full hover:bg-white hover:from-white hover:to-white  bg-gradient-to-b from-[#063065] to-[#3370BC]  text-white transition-all">
                Hire Expert
              </Link>
              <Link href="https://app.experthire.fr/" className="font-bold text-center text-[#31353B] text-sm sm:text-base px-4 sm:px-6 py-2 sm:py-3 border rounded-full hover:bg-gradient-to-b from-[#063065] to-[#3370BC] hover:text-white transition-all">
                Join Us
              </Link>
            </div>
          </div>
          <div className=" relative w-full lg:w-1/2 flex justify-center items-center">
            <div className="relative w-full max-w-[684px] aspect-[684/646]">
              <Image
                src={feature.image}
                alt={feature.title}
                fill
                className="object-cover"
                priority
              />
            </div>
            <div
              className={`absolute z-[-1] bottom-[-33%] ${
                index % 2 == 0 ? 'right-[3%]' : 'left-[3%]'
              } w-[400px] h-[400px] hidden md:block`}
            >
              <Image
                src="/features/dot-matrix.svg"
                alt="Dot background"
                width={400}
                height={400}
                className="w-full h-full opacity-80"
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
export default FeatureSection;
