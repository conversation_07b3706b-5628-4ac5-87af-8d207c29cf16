import Link from 'next/link';
import { Arrow<PERSON>eft, Search } from 'lucide-react';
import { Navbar, Footer } from '../../../_components';

export default function CategoryNotFound() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            {/* Back Link */}
            <div className="mb-8">
              <Link
                href="/blogs"
                className="inline-flex items-center text-[#063065] hover:text-[#094288] transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to All Blogs
              </Link>
            </div>

            {/* 404 Content */}
            <div className="bg-white rounded-lg shadow-sm p-12">
              <div className="flex justify-center mb-6">
                <Search className="w-16 h-16 text-gray-400" />
              </div>

              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Category Not Found
              </h1>

              <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                Sorry, we couldn&apos;t find any blog posts for this category.
                The category might not exist or there are no published posts in
                this category yet.
              </p>

              <div className="space-y-4">
                <Link
                  href="/blogs"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-full text-white bg-gradient-to-b from-primary to-[#3370BC] hover:from-[#094288] hover:to-[#2563EB] transition-all duration-200"
                >
                  Browse All Blogs
                </Link>

                <div className="text-sm text-gray-500">
                  or explore our available categories below
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
