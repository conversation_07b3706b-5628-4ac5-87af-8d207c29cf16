import { pricingData } from '@/lib/config/data';
import Link from 'next/link';

export default function PricingSection() {
  return (
    <div className="w-full max-w-7xl mx-auto py-30 px-4 overflow-hidden">
      <h2 className="text-center text-4xl text-primary font-bold mb-4 mt-10">
        Simple, transparent pricing
      </h2>
      <p className="text-center text-black mb-16">
        No contracts. No surprise fees.
      </p>
      <div className="flex flex-col lg:flex-row gap-24">
        {pricingData.map((plan, planIndex) => (
          <div key={planIndex} className="flex flex-row flex-1 gap-8">
            <div className="flex flex-col w-[40%]">
              <h3 className="text-primary text-2xl sm:text-4xl font-bold pt-4 pb-22">
                For {plan.type}
              </h3>
              {plan.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-8 mb-4">
                  <span className="text-md text-primary">{feature.name}</span>
                </div>
              ))}
            </div>
            <div className="flex w-[50%] items-center gap-4">
              <div className="min-w-[50%] h-[680px] sm:h-[650px] text-center bg-white rounded-[1rem] shadow-lg p-4 flex flex-col">
                <div>
                  <div className="font-medium text-md sm:text-xl text-primary mb-4">
                    Starter
                  </div>
                  <div className="flex items-center justify-center">
                    <span className="text-lg sm:text-3xl text-primary font-bold">
                      ${plan.pricing.starter.price}
                    </span>
                    <span className="text-gray-600 text-md sm:text-xl ml-1">
                      /{plan.pricing.starter.period}
                    </span>
                  </div>
                  <div className="mt-14 sm:mt-12">
                    {plan.features.map((feature, index) => (
                      <div
                        key={index}
                        className="flex justify-center gap-2 mb-8 sm:mb-6"
                      >
                        {feature.starter ? (
                          <svg
                            className="w-4 h-4 text-primary"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                          >
                            <path d="M20.285 2l-11.285 11.567-5.286-5.011-3.714 3.716 9 8.728 15-15.285z" />
                          </svg>
                        ) : (
                          <svg
                            className="w-4 h-4 text-red-500"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                          >
                            <path d="M24 20.188l-8.315-8.209 8.2-8.282-3.697-3.697-8.212 8.318-8.31-8.203-3.666 3.666 8.321 8.24-8.206 8.313 3.666 3.666 8.237-8.318 8.285 8.203z" />
                          </svg>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                <div className="mt-auto flex justify-center items-center">
                  <Link href="https://app.experthire.fr/" className="text-nowrap text-[12px] p-2 sm:text-sm sm:px-4 sm:py-2 font-semibold text-center text-white bg-gradient-to-b from-primary to-[#3370BC] border rounded-full hover:bg-white hover:from-white hover:to-white hover:text-black transition-all">
                    Start Free
                  </Link>
                </div>
              </div>
              <div className="min-w-[50%] h-[680px] sm:h-[650px] text-center bg-primary text-white rounded-[1rem] shadow-lg p-4 flex flex-col">
                <div>
                  <div className="font-medium text-md sm:text-xl mb-4">Pro</div>
                  <div className="flex items-center justify-center">
                    <span className="text-lg sm:text-3xl font-bold">
                      ${plan.pricing.pro.price}
                    </span>
                    <span className="text-gray-400 text-md sm:text-xl ml-1">
                      /{plan.pricing.pro.period}
                    </span>
                  </div>
                  <div className="mt-14 sm:mt-12">
                    {plan.features.map((feature, index) => (
                      <div
                        key={index}
                        className="flex justify-center gap-2 mb-8 sm:mb-6"
                      >
                        {feature.pro ? (
                          <svg
                            className="w-4 h-4"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                          >
                            <path d="M20.285 2l-11.285 11.567-5.286-5.011-3.714 3.716 9 8.728 15-15.285z" />
                          </svg>
                        ) : (
                          <svg
                            className="w-4 h-4 text-red-500"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                          >
                            <path d="M24 20.188l-8.315-8.209 8.2-8.282-3.697-3.697-8.212 8.318-8.31-8.203-3.666 3.666 8.321 8.24-8.206 8.313 3.666 3.666 8.237-8.318 8.285 8.203z" />
                          </svg>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                <div className="mt-auto flex justify-center items-center">
                  <Link href="https://app.experthire.fr/" className="text-[12px] p-2 sm:text-sm sm:px-4 sm:py-2 font-semibold text-center text-primary bg-white  border rounded-full hover:bg-gradient-to-b from-primary to-[#3370BC] hover:text-white transition-all">
                    Purchase
                  </Link>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
