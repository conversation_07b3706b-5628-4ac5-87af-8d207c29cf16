'use client';
import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { faqData } from '@/lib/config/data';

const FaqSection = () => {
  const [activeQuestion, setActiveQuestion] = useState<number>(0);
  const [answerHeight, setAnswerHeight] = useState<number>(0);
  const [answerScrollPosition, setAnswerScrollPosition] = useState<number>(0);
  const questionsRef = useRef<HTMLDivElement>(null);
  const answerContentRef = useRef<HTMLDivElement>(null);
  const answerContainerRef = useRef<HTMLDivElement>(null);
  const answerParagraphRef = useRef<HTMLParagraphElement>(null);

  const progressBarPosition = (activeQuestion / (faqData.length - 4)) * 100;

  const handleAnswerScroll = () => {
    if (answerContentRef.current && answerContainerRef.current) {
      const container = answerContainerRef.current;
      const content = answerContentRef.current;
      const scrollPercentage = (container.scrollTop / (content.scrollHeight - container.clientHeight)) * 100;
      setAnswerScrollPosition(scrollPercentage);
    }
  };

  useEffect(() => {
    if (questionsRef.current) {
      const activeElement = questionsRef.current.children[activeQuestion] as HTMLElement;
      if (activeElement) {
        const containerHeight = questionsRef.current.clientHeight;
        const elementOffset = activeElement.offsetTop;
        const elementHeight = activeElement.clientHeight;
        const scrollPosition = elementOffset - (containerHeight - elementHeight) / 2;
        
        questionsRef.current.scrollTo({
          top: scrollPosition,
          behavior: 'smooth'
        });
      }
    }

    if (answerParagraphRef.current && answerContainerRef.current) {
      const paragraphHeight = answerParagraphRef.current.offsetHeight;
      const trackHeight = answerContainerRef.current.clientHeight * 0.6; 
      const heightPercentage = (paragraphHeight / trackHeight) * 100;
      setAnswerHeight(heightPercentage);
      setAnswerScrollPosition(0);
      
      if (answerContainerRef.current) {
        answerContainerRef.current.scrollTop = 0;
      }
    }
  }, [activeQuestion]);

  return (
    <div className="min-h-[750px] bg-primary py-20 relative overflow-hidden">
      <div className="hidden lg:block lg:absolute lg:bottom-0 lg:left-[-1rem]">
        <Image
          src="/faq/rectangle.png"
          alt="Diagonal shape"
          height={435}
          width={435}
        />
      </div>
      <div className="max-w-7xl mx-auto px-4 relative z-10">
        <h2 className="text-4xl font-bold text-white text-center mb-12">
          Frequently Asked Questions
        </h2>
        <div className="hidden lg:block lg:absolute lg:top-20 lg:-left-4 lg:z-[-1]">
          <Image
            src="/faq/dottedShape.svg"
            alt="dotted shape"
            height={106}
            width={134}
          />
        </div>
        <div className="hidden lg:block lg:absolute lg:-bottom-125 lg:-right-4 lg:z-[-1]">
          <Image
            src="/faq/dottedShape.svg"
            alt="dotted shape"
            height={106}
            width={134}
          />
        </div>
        <div className="flex flex-col lg:flex-row gap-8 lg:relative lg:z-10">
          {/* Questions List */}
          <div className="w-full lg:h-[420px] lg:w-[50%] lg:absolute lg:top-6 lg:left-0 lg:z-10 bg-white shadow-lg rounded-2xl relative">
            <div className="absolute top-5 left-2 w-2 h-[90%] bg-primary/10 rounded-2xl">
              <div 
                className="w-2 bg-gradient-to-b from-primary to-[#3a7ccc] rounded-2xl transition-all duration-300"
                style={{
                  height: '34%',
                  transform: `translateY(${progressBarPosition}%)`
                }}
              />
            </div>
            <div 
              ref={questionsRef}
              className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-transparent"
            >
              {faqData.map((faq,index) => (
                <button
                  key={faq.id}
                  className={`w-full text-left text-sm lg:text-md p-5 transition-all duration-300 ${
                    index === 0 ? 'rounded-t-2xl' : ''
                  } ${
                    index === faqData.length - 1 ? 'rounded-b-2xl' : ''
                  } ${
                    activeQuestion === faq.id - 1
                      ? 'bg-primary/10 text-primary font-semibold'
                      : 'text-black hover:bg-primary/10'
                  }`}
                  onClick={() => setActiveQuestion(faq.id - 1)}
                >
                  <span className="flex pl-2 items-center gap-4">
                    <div
                      className={`w-5 h-5 shrink-0 rounded-2xl transition-all duration-300 ${
                        activeQuestion === faq.id - 1
                          ? 'bg-gradient-to-b from-primary to-[#3a7ccc] '
                          : 'bg-primary'
                      }`}
                    />
                    {faq.question}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Answer Card */}
          <div 
            ref={answerContainerRef}
            className="w-full h-[470px] p-6 lg:w-[55%] lg:absolute lg:top-0 lg:right-0 lg:pl-30 lg:pr-10 lg:pt-10 bg-white shadow-lg rounded-2xl relative text-wrap"
            onScroll={handleAnswerScroll}
          >
            <div className="absolute w-2 top-17 right-3 lg:top-23 lg:right-8 h-[70%] bg-primary/10 rounded-2xl">
              <div 
                className="w-2 bg-gradient-to-b from-primary to-[#3a7ccc] rounded-2xl transition-all duration-300"
                style={{
                  height: `${answerHeight}%`,
                  transform: `translateY(${answerScrollPosition}%)`
                }}
              />
            </div>
            <div className="h-full pr-2">
              <div 
                ref={answerContentRef}
                className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-transparent pr-4"
              >
                <h3 className="text-primary text-xl font-semibold mb-6">
                  {faqData[activeQuestion].question}
                </h3>
                <p 
                  ref={answerParagraphRef}
                  className="text-gray-600 text-md leading-relaxed"
                >
                  {faqData[activeQuestion].answer}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FaqSection;
