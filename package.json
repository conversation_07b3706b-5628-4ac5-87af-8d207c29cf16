{"name": "eh-cms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@portabletext/react": "^3.2.1", "@sanity/icons": "^3.7.0", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^3.91.0", "@types/react-slick": "^0.23.13", "lucide-react": "^0.487.0", "next": "15.3.0", "next-sanity": "^9.12.0", "nodemailer": "^7.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "react-slick": "^0.30.3", "sanity": "^3.91.0", "slick-carousel": "^1.8.1", "styled-components": "^6.1.17", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.0", "tailwindcss": "^4", "typescript": "^5"}}