import Image from 'next/image';
import { Search, MapPin } from 'lucide-react';
import Link from 'next/link';

const HeroSection = () => {
  return (
    <div className="relative w-full bg-primary">
      {/* Content */}
      <div className="hidden xl:block xl:absolute xl:top-0 xl:right-0">
        <Image
          src="/hero/heroBG.svg"
          alt="Hero Background"
          width={1000}
          height={1000}
          className="object-cover"
        />
      </div>
      <div className="max-w-[1440px] mx-auto px-4 sm:px-6 xl:px-8 pt-40 pb-20">
        <div className="grid xl:grid-cols-2 gap-[5rem] xl:gap-[12rem] items-center relative z-10">
          {/* Left Column - Text Content */}
          <div className="space-y-8 flex flex-col xl:items-start items-center">
            <h1 className="text-4xl text-center sm:text-5xl xl:text-left xl:text-6xl font-bold text-white max-w-2xl">
              Connecting experts across horizons
            </h1>
            <p className="text-lg text-center xl:text-left text-gray-200 max-w-2xl">
              Your businesses are hanging around the internet and in social
              media. They are engaging with their favorite brands on a whole new
              way and level. Do not let your business get left behind
            </p>

            {/* Trending Keywords */}
            <div className="flex-1 flex gap-3">
              <span className="text-sm text-gray-300">Trending Jobs:</span>
              <a href="https://app.experthire.fr/" className="text-sm text-white hover:text-blue-300">
                Web Designer
              </a>
              <a href="https://app.experthire.fr/" className="text-sm text-white hover:text-blue-300">
                UI/UX Designer
              </a>
              <a href="https://app.experthire.fr/" className="text-sm text-white hover:text-blue-300">
                Frontend
              </a>
            </div>

            {/* Search Bar */}
            <div className="flex flex-col w-full sm:flex-row gap-4 max-w-2xl bg-white p-2 rounded-2xl sm:rounded-full">
              <div className="flex-1 flex gap-2 px-3">
                <Search className="text-primary mt-1" size={25} />
                <input
                  type="text"
                  placeholder="Job title or keyword"
                  className="w-full outline-none"
                />
              </div>
              <div className="w-[2px] bg-gray-300 hidden sm:block" />
              <div className="flex-1 flex gap-2 px-3">
                <MapPin className="text-primary mt-1" size={25} />
                <input
                  type="text"
                  placeholder="Bandung, Indonesia"
                  className="w-full outline-none"
                />
              </div>
              <Link href="https://app.experthire.fr/" className="text-[12px] p-2 sm:text-sm sm:px-4 sm:py-2 font-semibold text-center text-white bg-gradient-to-b from-primary to-[#3370BC] border rounded-full hover:bg-white hover:from-white hover:to-white hover:text-black transition-all">
                Search
              </Link>
            </div>
          </div>

          {/* Right Column - Image */}
          <div className="relative mx-auto xl:mx-0 w-[300px] h-[370px] sm:w-[400px] sm:h-[470px] md:w-[500px] md:h-[570px] xl:h-[710px] rounded-2xl overflow-hidden">
            <Image
              src="/hero/hero-image.svg"
              alt="Team collaboration"
              fill
              className="object-contain"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
