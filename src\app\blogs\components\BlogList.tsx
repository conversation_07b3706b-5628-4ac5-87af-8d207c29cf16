'use client';

import { Suspense } from 'react';
import { Post } from '@/types/schema';
import { urlForImage } from '@/sanity/lib/image';
import Image from 'next/image';
import Link from 'next/link';
import BlogPagination from './BlogPagination';
import { usePagination } from '../hooks/usePagination';

interface BlogListProps {
  posts: Post[];
}

const POSTS_PER_PAGE = 6;

// Component that uses pagination hook (needs to be wrapped in Suspense)
const BlogListWithPagination = ({ posts }: BlogListProps) => {
  const {
    currentPage,
    totalPages,
    currentItems: currentPosts,
    startIndex,
    endIndex,
    goToPage,
  } = usePagination({
    data: posts,
    itemsPerPage: POSTS_PER_PAGE,
    enableUrlSync: true,
  });

  const handlePageChange = (page: number) => {
    goToPage(page);
    // Smooth scroll to top of blog section
    const blogSection = document.getElementById('blog-posts-section');
    if (blogSection) {
      blogSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  if (posts.length === 0) {
    return (
      <div className="text-center py-12 bg-white rounded-lg shadow-sm">
        <p className="text-gray-600 text-lg">No blog posts available yet.</p>
        <p className="text-gray-500 text-sm mt-2">
          Make sure your posts have valid slugs in Sanity Studio.
        </p>
      </div>
    );
  }

  return (
    <>
      {/* Posts Grid */}
      <div id="blog-posts-section" className="space-y-8">
        {currentPosts.map((post: Post) => (
          <article
            key={post._id}
            className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
          >
            <Link href={`/blogs/${post.slug!.current}`} className="group block">
              {/* Featured Image */}
              {post.mainImage && (
                <div className="relative h-64 w-full overflow-hidden">
                  <Image
                    src={urlForImage(post.mainImage).url()}
                    alt={post.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
              )}

              {/* Content */}
              <div className="p-6">
                {/* Meta Information */}
                <div className="flex items-center text-sm text-gray-500 mb-3">
                  {post.categories?.[0]?.title && (
                    <span className="inline-flex items-center mr-4">
                      <svg
                        className="w-4 h-4 mr-1"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                      {post.categories[0].title}
                    </span>
                  )}                  {/* Creation Time and Author */}
                  {post._createdAt && (
                    <span className="inline-flex items-center">
                      <svg
                        className="w-4 h-4 mr-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      {new Date(post._createdAt).toLocaleDateString('en-US', {
                        month: 'long',
                        day: 'numeric',
                        year: 'numeric'
                      })}
                      {post.author?.name && (
                        <span className="ml-1">
                          by <span className="text-[#063065]">{post.author.name}</span>
                        </span>
                      )}
                    </span>
                  )}
                </div>

                {/* Title */}
                <h2 className="text-xl md:text-2xl font-bold mb-3 text-gray-900 group-hover:text-[#063065] transition-colors leading-tight">
                  {post.title}
                </h2>

                {/* Excerpt */}
                {post.excerpt && (
                  <p className="text-gray-600 mb-4 leading-relaxed line-clamp-3">
                    {post.excerpt}
                  </p>
                )}

                {/* Read More Button */}
                <button className="inline-flex items-center px-4 py-2 border border-primary bg-gradient-to-b from-primary to-[#3370BC] text-white font-medium rounded-full hover:bg-white hover:from-white hover:to-white hover:text-primary transition-colors cursor-pointer">
                  Read More
                </button>
              </div>
            </Link>
          </article>
        ))}
      </div>

      {/* Pagination */}
      <BlogPagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
      />

      {/* Results Info */}
      <div className="mt-4 text-center text-sm text-gray-600">
        Showing {startIndex + 1} to {Math.min(endIndex, posts.length)} of{' '}
        {posts.length} posts
      </div>
    </>
  );
};

// Loading fallback component
const BlogListLoading = () => (
  <div className="space-y-8">
    {Array.from({ length: 6 }).map((_, index) => (
      <article
        key={index}
        className="bg-white rounded-lg shadow-sm overflow-hidden animate-pulse"
      >
        {/* Image Skeleton */}
        <div className="h-64 w-full bg-gray-200"></div>

        {/* Content Skeleton */}
        <div className="p-6">
          {/* Meta Information Skeleton */}
          <div className="flex items-center space-x-4 mb-3">
            <div className="h-4 bg-gray-200 rounded w-20"></div>
            <div className="h-4 bg-gray-200 rounded w-24"></div>
          </div>

          {/* Title Skeleton */}
          <div className="space-y-2 mb-3">
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
            <div className="h-6 bg-gray-200 rounded w-1/2"></div>
          </div>

          {/* Excerpt Skeleton */}
          <div className="space-y-2 mb-4">
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>

          {/* Button Skeleton */}
          <div className="h-10 bg-gray-200 rounded-full w-24"></div>
        </div>
      </article>
    ))}
  </div>
);

// Main component with Suspense wrapper
const BlogList = ({ posts }: BlogListProps) => {
  return (
    <Suspense fallback={<BlogListLoading />}>
      <BlogListWithPagination posts={posts} />
    </Suspense>
  );
};

export default BlogList;
