import { DocumentTextIcon } from '@sanity/icons';
import { defineArrayMember, defineField, defineType } from 'sanity';

export const postType = defineType({
  name: 'post',
  title: 'Post',
  type: 'document',
  icon: DocumentTextIcon,
  fields: [
    defineField({
      name: 'title',
      type: 'string',
    }),
    defineField({
      name: 'slug',
      type: 'slug',
      options: {
        source: 'title',
      },
    }),
    defineField({
      name: 'author',
      type: 'reference',
      to: { type: 'author' },
    }),
    defineField({
      name: 'mainImage',
      type: 'image',
      options: {
        hotspot: true,
      },
      fields: [
        defineField({
          name: 'alt',
          type: 'string',
          title: 'Alternative text',
        }),
      ],
    }),
    defineField({
      name: 'categories',
      type: 'array',
      of: [defineArrayMember({ type: 'reference', to: { type: 'category' } })],
    }),
    defineField({
      name: 'tags',
      type: 'array',
      title: 'Tags',
      description:
        'Add up to 2 relevant tags to help categorize and find your posts. Each tag can only be added once per post.',
      of: [
        defineArrayMember({
          type: 'string',
          validation: (Rule) => Rule.required().min(1).max(50),
        }),
      ],
      options: {
        layout: 'tags',
      },
      validation: (Rule) => [
        Rule.max(2).error(
          'Maximum 2 tags allowed per post. Please remove extra tags.'
        ),
        Rule.custom((tags: string[] | undefined) => {
          if (!tags || !Array.isArray(tags)) return true;

          // Enforce 2-tag limit
          if (tags.length > 2) {
            return `Too many tags! Maximum 2 tags allowed per post. You have ${tags.length} tags. Please remove ${tags.length - 2} tag(s).`;
          }

          // Check for empty tags
          if (tags.some((tag) => !tag || tag.trim() === '')) {
            return 'Empty tags are not allowed. Please remove empty tags.';
          }

          // Normalize tags for duplicate checking
          const normalizedTags = tags.map((tag) => tag.toLowerCase().trim());
          const uniqueTags = new Set(normalizedTags);

          if (normalizedTags.length !== uniqueTags.size) {
            // Find the duplicate tags
            const duplicates: string[] = [];
            const seen = new Set<string>();

            normalizedTags.forEach((tag) => {
              if (seen.has(tag) && !duplicates.includes(tag)) {
                duplicates.push(tag);
              }
              seen.add(tag);
            });

            return `Duplicate tags found: "${duplicates.join('", "')}". Each tag can only be used once per post.`;
          }

          // Check tag format recommendations
          const invalidFormatTags = tags.filter((tag) => {
            const trimmed = tag.trim();
            return (
              trimmed !== trimmed.toLowerCase() || // Contains uppercase
              trimmed.includes(' ') || // Contains spaces
              trimmed.length > 30 // Too long
            );
          });

          if (invalidFormatTags.length > 0) {
            return `Consider improving tag format: "${invalidFormatTags.join('", "')}". Use lowercase with hyphens (e.g., "web-development").`;
          }

          return true;
        }),
      ],
    }),
    defineField({
      name: 'excerpt',
      type: 'text',
      title: 'Excerpt',
      description: 'A brief description of the post',
    }),
    defineField({
      name: 'body',
      title: 'Body',
      type: 'blockContent',
      description: 'The main content of your blog post',
    }),
  ],
  preview: {
    select: {
      title: 'title',
      author: 'author.name',
      media: 'mainImage',
    },
    prepare(selection) {
      const { author } = selection;
      return { ...selection, subtitle: author && `by ${author}` };
    },
  },
});
