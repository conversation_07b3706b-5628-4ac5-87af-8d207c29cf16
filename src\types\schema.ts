import { Image, PortableTextBlock } from 'sanity';

export interface Author {
  _id: string;
  name: string;
  image?: Image;
  bio?: PortableTextBlock[];
}

export interface Category {
  _id: string;
  title: string;
  slug?: { current: string } | null;
  description?: string;
}

export interface Post {
  _id: string;
  title: string;
  slug?: { current: string } | null;
  mainImage?: Image;
  author?: Author;
  categories?: Category[];
  tags?: string[];
  _createdAt: string;
  excerpt?: string;
  body: PortableTextBlock[];
}
