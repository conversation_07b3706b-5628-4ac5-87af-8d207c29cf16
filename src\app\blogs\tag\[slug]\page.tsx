import { client } from '@/sanity/lib/client';
import { Post } from '@/types/schema';
import { Navbar, Footer } from '../../../_components';
import SimpleBlogList from '../../components/SimpleBlogList';
import TagsSection from '../../components/TagsSection';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { notFound } from 'next/navigation';
import type { Metadata } from 'next';

interface TagPageProps {
  params: Promise<{ slug: string }>;
}

async function getPostsByTag(tag: string): Promise<Post[]> {
  const result = await client.fetch(
    `*[_type == "post" && defined(slug.current) && $tag in tags] {
      _id,
      title,
      slug,
      mainImage,
      author->,
      categories[]->,
      tags,
      excerpt,
      body,
      _createdAt
    } | order(_createdAt desc)`,
    { tag } as Record<string, unknown>
  );
  return result || [];
}

export async function generateMetadata({
  params,
}: TagPageProps): Promise<Metadata> {
  const { slug } = await params;
  const tag = slug.replace(/-/g, ' ');

  return {
    title: `${tag} - Blog Posts`,
    description: `Browse all blog posts tagged with ${tag}`,
  };
}

export default async function TagPage({ params }: TagPageProps) {
  const { slug } = await params;

  // Convert slug back to tag (reverse of tagToSlug function)
  // Try both the slug as-is and with spaces for better matching
  const tagWithSpaces = slug.replace(/-/g, ' ').toLowerCase();
  const tagWithHyphens = slug.toLowerCase();

  // Try to get posts with both formats
  let posts = await getPostsByTag(tagWithHyphens);
  if (posts.length === 0) {
    posts = await getPostsByTag(tagWithSpaces);
  }

  const displayTag =
    posts.length > 0
      ? posts[0].tags?.find(
          (t) =>
            t.toLowerCase() === tagWithHyphens ||
            t.toLowerCase() === tagWithSpaces
        ) || tagWithSpaces
      : tagWithSpaces;

  if (posts.length === 0) {
    notFound();
  }

  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-gray-50">
        {/* Header */}
        <section className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 pt-20 pb-8 sm:px-6 lg:px-8">
            <Link
              href="/blogs"
              className="inline-flex items-center text-[#063065] hover:text-[#094288] transition-colors mb-6"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to All Posts
            </Link>

            <div className="mb-8">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Posts tagged with &ldquo;{displayTag}&rdquo;
              </h1>
              <p className="text-lg text-gray-600">
                {posts.length} post{posts.length !== 1 ? 's' : ''} found
              </p>
            </div>
          </div>
        </section>

        {/* Content */}
        <section className="py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Main Content */}
              <div className="lg:col-span-3">
                <SimpleBlogList posts={posts} />
              </div>

              {/* Sidebar */}
              <div className="space-y-8">
                {/* All Tags */}
                <TagsSection
                  posts={posts}
                  currentTag={displayTag}
                  maxTags={15}
                />

                {/* Tag Info */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">
                    About This Tag
                  </h3>
                  <div className="space-y-3 text-sm text-gray-600">
                    <div className="flex justify-between">
                      <span>Tag:</span>
                      <span className="font-medium text-gray-900">
                        {displayTag}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Posts:</span>
                      <span className="font-medium text-gray-900">
                        {posts.length}
                      </span>
                    </div>
                    <div className="pt-3 border-t border-gray-200">
                      <Link
                        href="/blogs"
                        className="text-[#063065] hover:text-[#094288] transition-colors"
                      >
                        View all posts →
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
