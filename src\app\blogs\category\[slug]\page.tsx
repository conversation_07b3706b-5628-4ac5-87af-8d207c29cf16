import { client } from '@/sanity/lib/client';
import {
  getPostsByCategoryQuery,
  getAllCategoriesQuery,
} from '@/sanity/lib/queries';
import { Post, Category } from '@/types/schema';
import { Navbar, Footer } from '../../../_components';
import SimpleBlogList from '../../components/SimpleBlogList';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { notFound } from 'next/navigation';
import type { Metadata } from 'next';

type tParams = Promise<{ slug: string }>;

async function getCategoryBySlug(slug: string): Promise<Category | null> {
  const result = await client.fetch(
    `*[_type == "category" && slug.current == $slug][0] {
      _id,
      title,
      slug,
      description
    }`,
    { slug }
  );
  return result || null;
}

// Generate metadata
export async function generateMetadata({
  params,
}: {
  params: tParams;
}): Promise<Metadata> {
  const { slug } = await params;

  const category = await getCategoryBySlug(slug);
  const categoryTitle =
    category?.title ||
    slug
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

  return {
    title: `${categoryTitle} - Blog Category`,
    description: `Browse all blog posts in the ${categoryTitle} category.`,
  };
}

async function getCategoryPosts(categorySlug: string): Promise<Post[]> {
  const result = await client.fetch(getPostsByCategoryQuery, { categorySlug });
  return result || [];
}

async function getAllCategories(): Promise<Category[]> {
  const result = await client.fetch(getAllCategoriesQuery);
  return result || [];
}

// Generate static params for all categories
export async function generateStaticParams() {
  const categories = await getAllCategories();

  return categories.map((category) => ({
    slug:
      category.slug?.current ||
      category.title.toLowerCase().replace(/\s+/g, '-'),
  }));
}

export default async function CategoryPage({ params }: { params: tParams }) {
  const { slug } = await params;

  // Get category data and posts
  const category = await getCategoryBySlug(slug);
  const posts = await getCategoryPosts(slug);

  // If no category found or no posts, show 404
  if (!category || posts.length === 0) {
    notFound();
  }

  const categoryTitle = category.title;

  return (
    <>
      <Navbar />
      <main className="min-h-screen">
        {/* Back to Blog Link */}
        <section className="bg-gray-50 py-8">
          <div className="max-w-7xl mx-auto px-4 pt-14 sm:px-6 lg:px-8">
            <Link
              href="/blogs"
              className="inline-flex items-center text-[#063065] hover:text-[#094288] transition-colors mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to All Blogs
            </Link>

            {/* Category Header */}
            <div className="mb-8">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                {categoryTitle}
              </h1>
              <p className="text-lg text-gray-600">
                Explore all blog posts in the {categoryTitle} category
              </p>
              <div className="mt-4 text-sm text-gray-500">
                {posts.length} {posts.length === 1 ? 'post' : 'posts'} found
              </div>
            </div>
          </div>
        </section>

        {/* Blog Posts */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col lg:flex-row gap-12">
              {/* Blog Posts */}
              <div className="lg:w-2/3">
                <SimpleBlogList posts={posts} />
              </div>

              {/* Sidebar */}
              <div className="lg:w-1/3">
                <div className="sticky top-8 space-y-8">
                  {/* Current Category Info */}
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-4">
                      Current Category
                    </h3>
                    <div className="flex items-center justify-between py-2">
                      <span className="text-[#063065] font-medium">
                        {categoryTitle}
                      </span>
                      <span className="text-xs bg-[#063065] text-white px-2 py-1 rounded">
                        {posts.length}
                      </span>
                    </div>
                  </div>

                  {/* Latest Posts from this category */}
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-4">
                      Latest in {categoryTitle}
                    </h3>
                    <div className="space-y-4">
                      {posts.slice(0, 3).map((post: Post) => (
                        <div
                          key={post._id}
                          className="border-b border-gray-100 last:border-b-0 pb-3 last:pb-0"
                        >
                          <Link
                            href={`/blogs/${post.slug!.current}`}
                            className="block hover:text-[#063065] transition-colors"
                          >
                            <h4 className="text-sm font-medium text-gray-900 line-clamp-2 mb-1">
                              {post.title}
                            </h4>{' '}
                            {post._createdAt && (
                              <p className="text-xs text-gray-500">
                                {new Date(post._createdAt).toLocaleDateString(
                                  'en-US',
                                  {
                                    month: 'long',
                                    day: 'numeric',
                                    year: 'numeric',
                                  }
                                )}
                                {post.author?.name && (
                                  <span className="ml-1">
                                    by{' '}
                                    <span className="text-[#063065]">
                                      {post.author.name}
                                    </span>
                                  </span>
                                )}
                              </p>
                            )}
                          </Link>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
