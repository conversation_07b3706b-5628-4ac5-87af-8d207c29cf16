'use client';

import { useEffect, useRef, useState } from 'react';
import { featureData } from '@/lib/config/data';

type UseScrollSpyOptions = {
  threshold?: number;
  rootMargin?: string;
};

export default function useScrollSpy({
  threshold = 0.3,
  rootMargin = '0px',
}: UseScrollSpyOptions = {}) {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const visibleEntries = entries.filter((entry) => entry.isIntersecting);
        if (visibleEntries.length > 0) {
          const mostVisible = visibleEntries.reduce((prev, curr) =>
            prev.intersectionRatio > curr.intersectionRatio ? prev : curr
          );
          setSelectedCategory(mostVisible.target.id);
        } else {
          setSelectedCategory(null);
        }
      },
      { root: null, rootMargin, threshold }
    );

    featureData.forEach((feature) => {
      const el = document.getElementById(feature.id);
      if (el) observer.observe(el);
    });

    observerRef.current = observer;

    return () => {
      observer.disconnect();
    };
  }, [threshold, rootMargin]);

  const handleScrollTo = (id: string) => {
    const el = document.getElementById(id);
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'start' });
      setSelectedCategory(id);
    }
  };
  return { selectedCategory, setSelectedCategory, handleClick: handleScrollTo };
}
