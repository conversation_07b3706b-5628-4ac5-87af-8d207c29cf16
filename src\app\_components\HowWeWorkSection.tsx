'use client';

import { useEffect, useState } from 'react';
import Slider from 'react-slick';
import Image from 'next/image';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { howWeWorkData } from '@/lib/config/data';

export default function HowWeWorkSection() {
  const [activeSlide, setActiveSlide] = useState(0);
  const [windowWidth, setWindowWidth] = useState(0);

  useEffect(() => {
    setWindowWidth(window.innerWidth);
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    arrows: false,
    cssEase: 'linear',
    beforeChange: (current: number, next: number) => setActiveSlide(next),
    appendDots: (dots: React.ReactNode) => (
      <div
        style={{
          position: 'absolute',
          bottom: windowWidth < 1280 ? '4%' : '25%',
          
        }}
      >
        {dots}
      </div>
    ),
    customPaging: (i: number) => (
      <div className="w-4 h-4 flex justify-center items-center">
        <div
          className={`w-4 h-4 rounded-full transition-all duration-300 ${
            i === activeSlide
              ? 'bg-gradient-to-b from-[#063065] to-[#3370BC]'
              : 'bg-[#CDDCFC]'
          }`}
        />
      </div>
    ),
  };

  return (
    <div className="relative w-full h-[750px] bg-primary text-white">
      <div className="hidden xl:block xl:absolute xl:-bottom-12 xl:left-0 xl:h-[435px] xl:w-[435px] xl:z-10 overflow-hidden">
        <Image
          src="/howWeWork/rectangle.png"
          alt="Diamond Shape"
          width={435}
          height={435}
        />
      </div>
      <div className="hidden xl:block xl:absolute xl:bottom-[-7rem] xl:right-[5rem]">
        <Image
          src="/howWeWork/dotVector.svg"
          alt="dotted shape"
          width={450}
          height={450}
        />
      </div>
      <div className="max-w-7xl mx-auto px-4">
        <Slider {...sliderSettings} className="how-work-slider">
          {howWeWorkData.map((item, index) => (
            <div key={index} className="outline-none">
              <div className="flex flex-col h-[750px] xl:flex-row items-center py-10 xl:items-start xl:py-20 gap-4">
                <div className="xl:w-[65%] xl:space-y-6 relative z-10">
                  <h2 className="text-xl text-center font-semibold xl:text-4xl xl:font-bold xl:text-left">
                    <span>How </span>
                    <span className="relative inline-block">
                      <span className="absolute -top-7 left-0 text-xl text-[#9B9B9B] font-semibold z-10 xl:-top-10 xl:text-4xl">
                        {item.type}
                      </span>
                      <span className="relative z-20">{item.type}</span>
                    </span>
                    <span> works</span>
                  </h2>
                  <p className="text-sm text-wrap font-medium max-w-[700px] text-center xl:text-xl xl:text-left xl:pt-4">
                    {item.description}
                  </p>
                  <div className="bg-[#063065] relative overflow-hidden">
                    <Image
                      src={item.workflowImage}
                      alt={`${item.type} workflow diagram`}
                      width={700}
                      height={200}
                      quality={100}
                      className="object-cover"
                    />
                  </div>
                </div>
                <div className="relative z-10 h-[350px] w-[280px] xl:h-[558px] xl:w-[453px] rounded-2xl xl:top-[-4rem]">
                  <Image
                    src={item.sideImage}
                    alt={`${item.type} side image`}
                    fill
                    className="object-cover"
                    priority
                  />
                </div>
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </div>
  );
}
