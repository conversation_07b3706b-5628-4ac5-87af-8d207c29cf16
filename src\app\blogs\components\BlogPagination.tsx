'use client';

interface BlogPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const BlogPagination = ({
  currentPage,
  totalPages,
  onPageChange,
}: BlogPaginationProps) => {
  // Don't render pagination if there's only one page or no pages
  if (totalPages <= 1) return null;

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      if (currentPage > 3) {
        pages.push('...');
      }

      // Show pages around current page
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);

      for (let i = start; i <= end; i++) {
        if (i !== 1 && i !== totalPages) {
          pages.push(i);
        }
      }

      if (currentPage < totalPages - 2) {
        pages.push('...');
      }

      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const pageNumbers = getPageNumbers();

  return (
    <div className="mt-12 flex justify-center">
      <nav className="relative" aria-label="Pagination">
        {/* Page Numbers Container */}
        <div className="flex border border-gray-300 rounded-sm overflow-hidden shadow-sm">
          {pageNumbers.map((page, index) => (
            <div key={index}>
              {page === '...' ? (
                <span className="flex items-center justify-center w-12 h-12 text-sm text-gray-500 bg-white border-r border-gray-300 last:border-r-0">
                  ...
                </span>
              ) : (
                <button
                  onClick={() => onPageChange(page as number)}
                  className={`
                    flex items-center justify-center w-12 h-12 text-sm font-medium transition-all duration-200 border-r border-gray-300 last:border-r-0
                    ${
                      currentPage === page
                        ? 'text-white bg-[#063065] relative z-10'
                        : 'text-[#063065] bg-white hover:bg-blue-50'
                    }
                  `}
                  aria-label={`Go to page ${page}`}
                  aria-current={currentPage === page ? 'page' : undefined}
                >
                  {String(page).padStart(2, '0')}
                </button>
              )}
            </div>
          ))}
        </div>

        {/* Blue bottom border for the entire pagination */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-[#4A90E2]" />
      </nav>
    </div>
  );
};

export default BlogPagination;
