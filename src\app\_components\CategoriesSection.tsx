'use client';
type Props = {
  categories: { id: string; title: string }[];
  selected: string | null;
  handleClick: (id: string) => void;
};
const JobCategoriesSection = ({ categories, selected, handleClick }: Props) => {
  return (
    <div className="bg-white flex flex-col justify-center items-center gap-[48px] mx-auto max-w-4xl py-24">
      <h2 className="text-2xl md:text-3xl font-bold text-center text-[#31353B]  ">
        Job Categories
      </h2>
      <div className="flex gap-6 flex-wrap justify-center items-center">
        {categories.map((category) => (
          <button
            key={category.id}
            className={`flex items-center justify-center font-[20px] rounded-[50px] h-[30px] px-4 py-2 border border-[#CDDCFC] hover:bg-[linear-gradient(180deg,_#063065_0%,_#3370BC_100%))] hover:text-[#FFFFFF] transition ${
              selected === category.id
                ? 'bg-[linear-gradient(180deg,_#063065_0%,_#3370BC_100%))] text-[#FFFFFF]'
                : ''
            }`}
            onClick={() => handleClick(category.id)}
          >
            {category.title}
          </button>
        ))}
      </div>
    </div>
  );
};

export default JobCategoriesSection;
