import { sanityFetch } from '@/sanity/lib/live';
import { getAllPostsQuery } from '@/sanity/lib/queries';
import { urlForImage } from '@/sanity/lib/image';
import { Post } from '@/types/schema';
import Image from 'next/image';
import Link from 'next/link';
import { Navbar, Footer } from '../_components';
import SimpleBlogList from './components/SimpleBlogList';
import TagsSection from './components/TagsSection';

async function getBlogs(): Promise<Post[]> {
  const posts = await sanityFetch({
    query: getAllPostsQuery,
  });
  return posts.data;
}

export default async function BlogsPage() {
  const posts = await getBlogs();

  // Filter out posts without valid slugs as an extra safety measure
  const validPosts = posts.filter((post) => post.slug?.current);

  return (
    <>
      <Navbar />
      <main className="min-h-screen">
        {/* Hero Section */}
        <div className="relative w-full bg-primary">
          {/* Content */}
          <div className="hidden xl:block xl:absolute xl:top-[-5rem] xl:right-0">
            <Image
              src="/hero/heroBG.svg"
              alt="Hero Background"
              width={1000}
              height={800}
              className="object-cover"
            />
          </div>
          <div className="max-w-[1440px] mx-auto px-4 sm:px-6 xl:px-8 pt-20">
            <div className="grid xl:grid-cols-2 gap-[5rem] xl:gap-[1rem] items-center relative z-10">
              {/* Left Column - Text Content */}
              <div className="space-y-8 flex flex-col xl:items-start items-center">
                <h1 className="text-4xl text-center sm:text-5xl xl:text-left xl:text-6xl font-bold text-white max-w-2xl">
                  Blogs
                </h1>
                <p className="text-lg text-center xl:text-left text-gray-200 max-w-2xl">
                  Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                  Tempora consectetur corrupti modi maxime minima aut enim nisi
                  maiores molestiae fuga.
                </p>
              </div>

              {/* Right Column - Image */}
              <div className="relative mx-auto xl:mx-0 w-[300px] h-[370px] sm:w-[400px] sm:h-[470px] md:w-[500px] md:h-[570px] xl:h-[686px] xl:w-[748px] rounded-2xl overflow-hidden">
                <Image
                  src="/hero/blog-image.svg"
                  alt="Team collaboration"
                  fill
                  className="object-contain"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col lg:flex-row gap-12">
              {/* Blog Posts */}
              <div className="lg:w-2/3">
                <SimpleBlogList posts={validPosts} />
              </div>

              {/* Sidebar */}
              <div className="lg:w-1/3">
                <div className="sticky top-8 space-y-8">
                  {/* Latest Posts */}
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-4">
                      Latest Post
                    </h3>
                    <div className="space-y-4">
                      {validPosts.slice(0, 4).map((post: Post) => (
                        <div
                          key={post._id}
                          className="flex items-start space-x-3"
                        >
                          <div className="flex-shrink-0">
                            {post.mainImage && (
                              <Image
                                src={urlForImage(post.mainImage).url()}
                                alt={post.title}
                                width={60}
                                height={60}
                                className="rounded object-cover"
                              />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <Link
                              href={`/blogs/${post.slug!.current}`}
                              className="block hover:text-[#063065] transition-colors"
                            >
                              <h4 className="text-sm font-medium text-gray-900 line-clamp-2 mb-1">
                                {post.title}
                              </h4>{' '}
                              {post._createdAt && (
                                <p className="text-xs text-gray-500">
                                  {new Date(post._createdAt).toLocaleDateString(
                                    'en-US',
                                    {
                                      month: 'long',
                                      day: 'numeric',
                                      year: 'numeric',
                                    }
                                  )}
                                  {post.author?.name && (
                                    <span className="ml-1">
                                      by{' '}
                                      <span className="text-[#063065]">
                                        {post.author.name}
                                      </span>
                                    </span>
                                  )}
                                </p>
                              )}
                            </Link>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Categories */}
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-4">
                      Categories
                    </h3>
                    <div className="space-y-2">
                      {Array.from(
                        new Map(
                          validPosts.flatMap(
                            (post) =>
                              post.categories?.map((cat) => [cat.title, cat]) ||
                              []
                          )
                        ).values()
                      ).map((category) => (
                        <div
                          key={category._id}
                          className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
                        >
                          <Link
                            href={`/blogs/category/${
                              category.slug?.current ||
                              category.title.toLowerCase().replace(/\s+/g, '-')
                            }`}
                            className="text-gray-700 hover:text-[#063065] cursor-pointer transition-colors font-medium"
                          >
                            {category.title}
                          </Link>
                          <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                            {
                              validPosts.filter((post) =>
                                post.categories?.some(
                                  (cat) => cat._id === category._id
                                )
                              ).length
                            }
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Tags */}
                  <TagsSection posts={validPosts} maxTags={15} />
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
