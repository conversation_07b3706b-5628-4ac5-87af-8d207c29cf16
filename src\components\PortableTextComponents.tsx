import Image from 'next/image';
import Link from 'next/link';
import { urlForImage } from '@/sanity/lib/image';
import { PortableTextComponents } from '@portabletext/react';

export const portableTextComponents: PortableTextComponents = {
  types: {
    image: ({ value }) => {
      if (!value?.asset?._ref) {
        return null;
      }

      const imageUrl = urlForImage(value).url();
      const altText = value.alt || 'Blog image';

      return (
        <div className="my-8">
          <Image
            src={imageUrl}
            alt={altText}
            width={800}
            height={400}
            className="rounded-lg w-full h-auto"
          />
          {value.alt && (
            <p className="text-sm text-gray-600 text-center mt-2 italic">
              {altText}
            </p>
          )}
        </div>
      );
    },
  },
  marks: {
    link: ({ children, value }) => {
      if (!value?.href) {
        return <span className="text-blue-600">{children}</span>;
      }

      const href = value.href;
      const isExternal = !href.startsWith('/');

      return (
        <Link
          href={href}
          rel={isExternal ? 'noreferrer noopener' : undefined}
          target={value?.blank ? '_blank' : undefined}
          className="text-blue-600 hover:text-blue-800 underline"
        >
          {children}
        </Link>
      );
    },
    strong: ({ children }) => <strong className="font-bold">{children}</strong>,
    em: ({ children }) => <em className="italic">{children}</em>,
    code: ({ children }) => (
      <code className="bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm font-mono">
        {children}
      </code>
    ),
    underline: ({ children }) => <span className="underline">{children}</span>,
    'strike-through': ({ children }) => (
      <span className="line-through">{children}</span>
    ),
  },
  block: {
    normal: ({ children }) => (
      <p className="mb-4 leading-relaxed text-gray-800">{children}</p>
    ),
    h1: ({ children }) => (
      <h1 className="text-4xl font-bold mb-6 mt-8 text-gray-900 leading-tight border-b border-gray-200 pb-2">
        {children}
      </h1>
    ),
    h2: ({ children }) => (
      <h2 className="text-3xl font-bold mb-5 mt-7 text-gray-900 leading-tight">
        {children}
      </h2>
    ),
    h3: ({ children }) => (
      <h3 className="text-2xl font-bold mb-4 mt-6 text-gray-900 leading-tight">
        {children}
      </h3>
    ),
    h4: ({ children }) => (
      <h4 className="text-xl font-bold mb-3 mt-5 text-gray-900 leading-tight">
        {children}
      </h4>
    ),
    h5: ({ children }) => (
      <h5 className="text-lg font-bold mb-3 mt-4 text-gray-900 leading-tight">
        {children}
      </h5>
    ),
    h6: ({ children }) => (
      <h6 className="text-base font-bold mb-2 mt-3 text-gray-900 leading-tight">
        {children}
      </h6>
    ),
    blockquote: ({ children }) => (
      <blockquote className="border-l-4 border-blue-500 pl-6 my-6 italic text-gray-700 bg-gray-50 py-4 rounded-r">
        {children}
      </blockquote>
    ),
  },
  list: {
    bullet: ({ children }) => (
      <ul className="list-disc list-outside mb-6 ml-6 space-y-2 text-gray-800">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="list-decimal list-outside mb-6 ml-6 space-y-2 text-gray-800">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="leading-relaxed pl-2">{children}</li>
    ),
    number: ({ children }) => (
      <li className="leading-relaxed pl-2">{children}</li>
    ),
  },
};
