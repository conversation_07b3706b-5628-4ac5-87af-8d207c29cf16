'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import Image from 'next/image';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { ContactFormData, contactSchema } from '@/lib/schemas/contactSchema';

const ContactSection = () => {
  const [status, setStatus] = useState<string | null>(null);
  const [isError, setIsError] = useState(false);
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
  });
  const onSubmit = async (data: ContactFormData) => {
    setStatus(null);
    setIsError(false);
    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (response.ok) {
        setStatus('Message sent successfully!');
        setIsError(false);
        reset();
      } else {
        const errorData = await response.json();
        setStatus(errorData.message || 'Something went wrong.');
        setIsError(true);
      }
    } catch (error) {
      if (error instanceof Error) {
        setStatus(error.message);
      } else {
        setStatus('Something went wrong. Please try again later.');
      }
      setIsError(true);
    }
  };
  return (
    <div className="relative h-[950px] bg-white flex flex-col lg:flex-row items-center justify-center py-10">
      <div className="absolute hidden md:block top-16 md:top-[19.5%] md:right-[calc(50vw - 200px + 150px)] lg:right-[41%] z-10">
        <Image
          src="/contact/dotted-shape.svg"
          alt="Dotted Shape"
          width={106}
          height={134}
        />
      </div>
      <div className="relative md:bg-[#063065] w-full h-full md:h-[254px] text-white py-16 px-8 lg:py-32 lg:px-24 flex items-center justify-center lg:items-center lg:justify-center lg:gap-12 overflow-hidden">
        <div className="absolute hidden md:block md:left-0 md:top-0 md:w-full md:h-full">
          <Image
            src="/contact/background-shadow.svg"
            alt="Contact Background"
            fill
            className="opacity-90"
            style={{ objectPosition: 'left', objectFit: 'contain' }}
            priority
          />
        </div>
      </div>
      <div className="absolute bg-[#063065] md:bg-transparent  flex flex-col md:flex-row justify-center items-center md:gap-12 lg:gap-[230px] w-full py-12 md:py-0 px-4">
        <div className="mb-4 md:mb-0">
          <div className="z-10 text-center lg:text-left">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white">
              Get In Touch With Us
            </h2>
          </div>
        </div>

        <div className="bg-white shadow-xl z-10 rounded-xl p-6 sm:p-8 w-full max-w-[470px]">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
            <input
              type="text"
              placeholder="Your Name"
              {...register('name')}
              className="w-full px-4 py-3 rounded-md shadow-[0_8px_32px_-3px_rgba(46,105,178,0.10)] bg-white border-none outline-none placeholder-[#9B9B9B] text-sm focus:bg-blue-50"
            />
            {errors.name && (
              <p className="text-red-500 text-sm">{errors.name.message}</p>
            )}

            <input
              type="email"
              placeholder="Your Email"
              {...register('email')}
              className="w-full px-4 py-3 rounded-md shadow-[0_8px_32px_-3px_rgba(46,105,178,0.10)] bg-white border-none outline-none placeholder-[#9B9B9B] text-sm focus:bg-blue-50"
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email.message}</p>
            )}
            <input
              type="phone"
              placeholder="Your Phone"
              {...register('phone')}
              className="w-full px-4 py-3 rounded-md shadow-[0_8px_32px_-3px_rgba(46,105,178,0.10)] bg-white border-none outline-none placeholder-[#9B9B9B] text-sm focus:bg-blue-50"
            />
            {errors.phone && (
              <p className="text-red-500 text-sm">{errors.phone.message}</p>
            )}
            <textarea
              placeholder="Your Message"
              rows={4}
              {...register('message')}
              className="w-full px-4 py-3 rounded-md shadow-[0_8px_32px_-3px_rgba(46,105,178,0.10)] bg-white border-none outline-none placeholder-[#9B9B9B] text-sm resize-none focus:bg-blue-50 "
            ></textarea>
            {errors.message && (
              <p className="text-red-500 text-sm">{errors.message.message}</p>
            )}
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full py-3 rounded-full text-white text-sm font-medium bg-gradient-to-b from-[#063065] to-[#3370BC] hover:to-blue-950 transition"
            >
              {isSubmitting ? 'Sending...' : 'Send Message'}
            </button>
            {status && (
              <p
                className={`text-sm ${
                  isError ? 'text-red-500' : 'text-green-700'
                } mt-2 text-center`}
              >
                {status}
              </p>
            )}
          </form>
        </div>
      </div>
    </div>
  );
};

export default ContactSection;
