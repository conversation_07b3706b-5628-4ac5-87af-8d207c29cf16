import Link from 'next/link';
import {
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Phone,
  Mail,
  MapPin,
} from 'lucide-react';
import Image from 'next/image';

const Footer = () => {
  return (
    <footer className="relative bg-[#063065] min-h-[638px] w-full">
      {/* Background decorative elements */}
      <div className="absolute h-full md:w-[495px] inset-0 overflow-hidden">
        <div className="hidden md:block">
          <Image
            src="/footer/footer.svg"
            alt="Footer Background"
            fill
            className="object-cover"
            priority
          />
        </div>
        <div className=" relative md:absolute md:top-[40%] md:left-[15%] max-md:text-center max-md:items-center flex flex-col gap-6 md:gap-8 z-10 md:max-w-[310px] max-md:p-6">
          <div className=" flex flex-col gap-2 max-md:items-center">
            <div className="h-[30px] w-[130px] inset-0 overflow-hidden">
              <Image
                src="/footer/footer2.svg"
                alt="Footer Background"
                height={30}
                width={130}
                className="object-contain"
                priority
              />
            </div>
            <p className="text-white text-base font-medium leading-tight md:leading-[22px]">
              Empowering talent and opportunities to drive exceptional outcomes
            </p>
          </div>
          <div className="flex gap-5 md:gap-7">
            <Link href="#" className="text-white hover:text-gray-200">
              <Facebook size={22} />
            </Link>
            <Link href="#" className="text-white hover:text-gray-200">
              <Twitter size={22} />
            </Link>
            <Link href="#" className="text-white hover:text-gray-200">
              <Instagram size={22} />
            </Link>
            <Link href="#" className="text-white hover:text-gray-200">
              <Linkedin size={22} />
            </Link>
          </div>
        </div>
      </div>

      {/* Footer content */}
      <div className="relative max-md:text-center px-6 md:pl-[400px] pt-[250px]">
        <div className=" mx-auto max-w-fit grid grid-cols-1 sm:grid-cols-2 md:justify-center md:items-center md:flex md:flex-wrap gap-12 md:gap-[62px]">
          {/* Brand section */}

          {/* About Us section */}
          <div className="flex flex-col gap-2 ">
            <h3 className="text-white  text-lg font-medium">About Us</h3>
            <div className="flex flex-col gap-2 text-white text-base font-medium">
              <Link href="#">Company Overview</Link>
              <Link href="/terms-of-service">Terms of Service</Link>
              <Link href="/privacy-policy">Privacy Policy</Link>
            </div>
          </div>

          {/* Support section */}
          <div className="flex flex-col gap-2">
            <h3 className="text-white text-lg font-medium">Support</h3>
            <div className="flex flex-col gap-2 text-white text-base font-medium">
              <Link href="#">FAQ</Link>
              <Link href="#">Posting a Job</Link>
              <Link href="#">Finding Jobs</Link>
            </div>
          </div>

          {/* Community section */}
          <div className="flex flex-col gap-2">
            <h3 className="text-white text-lg font-medium">Community</h3>
            <div className="flex flex-col gap-2 text-white text-base font-medium">
              <Link href="#">Blog</Link>
              <Link href="#">Invite a friend</Link>
              <Link href="#">Become an Expert</Link>
            </div>
          </div>

          {/* Contact Us section */}
          <div className="flex flex-col gap-2">
            <h3 className="text-white text-lg font-medium">Contact Us</h3>
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-4 text-white">
                <Phone size={16} />
                <span>+25470000000</span>
              </div>
              <div className="flex items-center gap-4 text-white">
                <Mail size={16} />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-4 text-white">
                <MapPin size={16} />
                <span>
                  25 rue louis savoie
                  <br />
                  Ermont 95120 France.
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
      </div>
      <div className=" text-white text-base md:text-lg text-center py-6 md:my-30">
        {new Date().getFullYear()} ExpertHire. All right reserved
      </div>
    </footer>
  );
};

export default Footer;
